import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../../prisma/prisma.module';
import { OpenaiModule } from '../../openai/openai.module';
import { ILlmService } from './interfaces/llm.service.interface';
import { OpenaiLlmService } from './services/openai-llm.service';
import { GoogleGeminiLlmService } from './services/google-gemini-llm.service';
import { AnthropicLlmService } from './services/anthropic-llm.service';
import { EnhancedAnthropicLlmService } from '../../chat/services/enhanced-anthropic-llm.service';
import { LlmFactoryService } from './services/llm-factory.service';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    OpenaiModule, // For OpenaiService dependency
  ],
  providers: [
    OpenaiLlmService,
    GoogleGeminiLlmService,
    AnthropicLlmService,
    EnhancedAnthropicLlmService,
    LlmFactoryService,
    {
      provide: 'ILlmService',
      useFactory: async (llmFactory: LlmFactoryService): Promise<ILlmService> => {
        return llmFactory.getLlmService();
      },
      inject: [LlmFactoryService],
    },
  ],
  exports: ['ILlmService', LlmFactoryService],
})
export class LlmModule {}
